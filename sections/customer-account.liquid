<!-- /sections/customer-account.liquid -->

<section class="account">
  <div class="wrapper">
    <div class="grid grid--account">
      {%- render 'account-menu' -%}

      <div class="account-main">
        <h2 class="page__heading">{{ 'customer.account.title' | t }}</h2>

        <div class="account-details">
          <div class="account-info">
            <h3 class="h4">{{ 'customer.account.details' | t }}</h3>

            <div class="form-field">
              <label>{{ 'customer.register.first_name' | t }}</label>
              <p>{{ customer.first_name | default: '—' }}</p>
            </div>

            <div class="form-field">
              <label>{{ 'customer.register.last_name' | t }}</label>
              <p>{{ customer.last_name | default: '—' }}</p>
            </div>

            <div class="form-field">
              <label>{{ 'customer.register.email' | t }}</label>
              <p>{{ customer.email }}</p>
            </div>

            {% if customer.phone %}
              <div class="form-field">
                <label>{{ 'customer.addresses.phone' | t }}</label>
                <p>{{ customer.phone }}</p>
              </div>
            {% endif %}

            <div class="form-field">
              <label>{{ 'customer.account.member_since' | t | default: 'Member since' }}</label>
              <p>{{ customer.created_at | date: format: 'full_date' }}</p>
            </div>
          </div>

          <div class="account-actions">
            <h3 class="h4">{{ 'customer.account.quick_actions' | t | default: 'Quick Actions' }}</h3>

            <div class="account-links">
              <a href="{{ routes.account_addresses_url }}" class="btn btn--secondary">
                {{ 'customer.account.view_addresses' | t }}
              </a>

              {% if customer.orders.size > 0 %}
                <a href="#orders" class="btn btn--secondary" onclick="showOrdersSection()">
                  {{ 'customer.orders.title' | t }} ({{ customer.orders.size }})
                </a>
              {% endif %}
            </div>
          </div>
        </div>

        {% if customer.orders.size > 0 %}
          <div id="orders-section" class="orders-section" style="display: none; margin-top: 2rem;">
            <h3 class="h4">{{ 'customer.orders.recent_orders' | t | default: 'Recent Orders' }}</h3>
            {% assign recent_orders = customer.orders | slice: 0, 5 %}
            <table>
              <thead>
                <tr>
                  <th class="order-number">{{ 'customer.orders.order_number' | t }}</th>
                  <th class="date">{{ 'customer.orders.date' | t }}</th>
                  <th class="payment-status">{{ 'customer.orders.payment_status' | t }}</th>
                  <th class="total">{{ 'customer.orders.total' | t }}</th>
                </tr>
              </thead>
              <tbody>
                {% for order in recent_orders %}
                  <tr class="{% cycle 'odd', 'even' %} {% if order.cancelled %}cancelled_order{% endif %}">
                    <td class="order-number">
                      <span>{{ order.name | link_to: order.customer_url }}</span>
                    </td>
                    <td class="date">
                      <span class="note">{{ order.created_at | date: format: 'full_date' }}</span>
                    </td>
                    <td class="payment-status">
                      <span>{{ order.financial_status_label }}</span>
                    </td>
                    <td class="total">
                      <span class="total money">
                        {%- if order.total_price == 0 -%}
                          {{ 'general.money.free' | t }}
                        {%- else -%}
                          {{ order.total_price | money_with_currency }}
                        {%- endif -%}
                      </span>
                    </td>
                  </tr>
                {% endfor %}
              </tbody>
            </table>

            {% if customer.orders.size > 5 %}
              <p style="margin-top: 1rem;">
                <a href="#" onclick="showAllOrders()" class="text-link">
                  {{ 'customer.orders.view_all' | t | default: 'View all orders' }} ({{ customer.orders.size }})
                </a>
              </p>
            {% endif %}

            <div id="all-orders" style="display: none; margin-top: 1rem;">
              {% paginate customer.orders by 20 %}
                <table>
                  <thead>
                    <tr>
                      <th class="order-number">{{ 'customer.orders.order_number' | t }}</th>
                      <th class="date">{{ 'customer.orders.date' | t }}</th>
                      <th class="payment-status">{{ 'customer.orders.payment_status' | t }}</th>
                      <th class="fulfillment-status">{{ 'customer.orders.fulfillment_status' | t }}</th>
                      <th class="total">{{ 'customer.orders.total' | t }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {% for order in customer.orders %}
                      <tr class="{% cycle 'odd', 'even' %} {% if order.cancelled %}cancelled_order{% endif %}">
                        <td class="order-number">
                          <span>{{ order.name | link_to: order.customer_url }}</span>
                        </td>
                        <td class="date">
                          <span class="note">{{ order.created_at | date: format: 'full_date' }}</span>
                        </td>
                        <td class="payment-status">
                          <span>{{ order.financial_status_label }}</span>
                          {% if order.cancelled %}
                            {% assign order_cancelled_at = order.cancelled_at | date: format: 'full_date_and_time' %}
                            <p class="small highlight">
                              {{ 'customer.order.cancelled' | t: date: order_cancelled_at }}
                              <br>
                              {{ 'customer.order.cancelled_reason' | t: reason: order.cancel_reason }}
                            </p>
                          {% endif %}
                        </td>
                        <td class="fulfillment-status">
                          <span>{{ order.fulfillment_status_label }}</span>
                        </td>
                        <td class="total">
                          <span class="total money">
                            {%- if order.total_price == 0 -%}
                              {{ 'general.money.free' | t }}
                            {%- else -%}
                              {{ order.total_price | money_with_currency }}
                            {%- endif -%}
                          </span>
                        </td>
                      </tr>
                    {% endfor %}
                  </tbody>
                </table>

                {% render 'pagination', paginate: paginate %}
              {% endpaginate %}
            </div>
          </div>
        {% else %}
          <div class="no-orders" style="margin-top: 2rem;">
            <h3 class="h4">{{ 'customer.orders.title' | t }}</h3>
            <p>{{ 'customer.orders.none' | t }}</p>
          </div>
        {% endif %}
      </div>
    </div>
  </div>
</section>

<script>
  function showOrdersSection() {
    const ordersSection = document.getElementById('orders-section');
    if (ordersSection) {
      ordersSection.style.display = ordersSection.style.display === 'none' ? 'block' : 'none';
    }
  }

  function showAllOrders() {
    const allOrders = document.getElementById('all-orders');
    if (allOrders) {
      allOrders.style.display = allOrders.style.display === 'none' ? 'block' : 'none';
    }
  }
</script>

{% schema %}
  {
    "name": "Account",
    "blocks": [
      {
        "type": "@app"
      },
      {
        "type": "account",
        "name": "Account links"
      },
      {
        "type": "email",
        "name": "Email link",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Email title",
            "default": "Support"
          },
          {
            "type": "text",
            "id": "link",
            "label": "Link",
            "info": "Leave blank to use the store email address"
          }
        ]
      },
      {
        "type": "link",
        "name": "Link",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Link title",
            "default": "Home"
          },
          {
            "type": "url",
            "id": "link",
            "label": "Link",
            "default": "/"
          }
        ]
      }
    ]
  }
{% endschema %}
