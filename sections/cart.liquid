<!-- /templates/cart.liquid -->
{%- liquid
  assign animation_name = 'cart-items-fade'
  assign animation_duration = 500
  assign animation_delay = 100
  assign animation_delay_initial = 100
  assign animation_delay_increment = 50
  assign animation_cart_empty_delay = 100
  assign cart_item_count = cart.item_count | plus: 0
  assign empty_cart_products = settings.empty_cart_products | default: null
  assign show_empty_cart_menu = settings.show_empty_cart_menu
  assign empty_cart_menu = settings.empty_cart_menu
  assign color_scheme = 'color-' | append: section.settings.color_scheme
  assign free_shipping_block = section.blocks | where: 'type', 'free-shipping'
  assign is_free_shipping_first = false
  if section.blocks[0] == free_shipping_block[0]
    assign is_free_shipping_first = true
  endif

  if cart_item_count == 0
    assign empty_cart = true
  endif
-%}

{%- capture free_shipping -%}
  {%- assign block = free_shipping_block[0] -%}
  {%- if settings.free_shipping_limit != blank and block.settings.message != blank -%}
    {%- render 'free-shipping' message: block.settings.message, gradient: block.settings.free_shipping_gradient, show_wheel: false, show_progress_bar: true -%}
  {%- endif -%}
{%- endcapture -%}

{%- style -%}
  .cart {
    --PT: {{ section.settings.padding_top }}px;
    --PB: {{ section.settings.padding_bottom }}px;
  }
{%- endstyle -%}
<div class="cart-holder {{ color_scheme }}">
  <div
    class="cart{% if settings.quantity_style == 'dropdown' %} cart--no-overflow{% endif %} section-padding{% if empty_cart %} is-empty{% endif %}"
    data-section-id="{{ section.id }}"
    data-section-type="cart"
    data-cart-page
  >
    <div class="cart__empty" data-cart-empty>
      <h1
        class="cart__title"
        data-animation="{{ animation_name }}"
        data-animation-duration="{{ animation_duration }}"
        data-animation-delay="{{ animation_cart_empty_delay }}"
        {%- assign animation_cart_empty_delay = animation_cart_empty_delay | plus: animation_delay_increment -%}
      >
        {{- 'cart.general.title' | t -}}
      </h1>

      <p
        data-animation="{{ animation_name }}"
        data-animation-duration="{{ animation_duration }}"
        data-animation-delay="{{ animation_cart_empty_delay }}"
        {%- assign animation_cart_empty_delay = animation_cart_empty_delay | plus: animation_delay_increment -%}
      >
        {{- 'cart.general.empty' | t -}}
      </p>

      {%- if show_empty_cart_menu and linklists[empty_cart_menu].links.size > 0 -%}
        <div class="cart__empty__buttons">
          {%- for link in linklists[empty_cart_menu].links -%}
            <div
              class="cart__empty__item"
              data-animation="{{ animation_name }}"
              data-animation-duration="{{ animation_duration }}"
              data-animation-delay="{{ animation_cart_empty_delay }}"
              {%- assign animation_cart_empty_delay = animation_cart_empty_delay | plus: animation_delay_increment -%}
            >
              <a href="{{ link.url }}" class="btn btn--outline btn--primary">
                {{- link.title | strip_html | escape -}}
              </a>
            </div>
          {%- endfor -%}
        </div>
      {%- else -%}
        <div
          class="cart__empty__item"
          data-animation="{{ animation_name }}"
          data-animation-duration="{{ animation_duration }}"
          data-animation-delay="{{ animation_cart_empty_delay }}"
          {%- assign animation_cart_empty_delay = animation_cart_empty_delay | plus: animation_delay_increment -%}
        >
          <a href="{{ routes.all_products_collection_url }}" class="btn btn--outline btn--primary">
            <span>{{ 'cart.general.continue_shopping' | t }}</span>
          </a>
        </div>
      {%- endif -%}

      {%- if empty_cart_products -%}
        <div
          class="cart__empty__product"
          data-animation="{{ animation_name }}"
          data-animation-duration="{{ animation_duration }}"
          data-animation-delay="{{ animation_cart_empty_delay }}"
        >
          {%- liquid
            assign animation_cart_empty_delay = animation_cart_empty_delay | plus: animation_delay_increment

            if empty_cart_products != blank
              for upsell_product in empty_cart_products
                render 'upsell-product', upsell_product: upsell_product
              endfor
            endif
          -%}
        </div>
      {%- endif -%}

      {%- if settings.show_recently_viewed_products -%}
        <script src="{{ 'recently-viewed.js' | asset_url }}" defer="defer"></script>

        <recently-viewed
          class="cart__empty__product product__upsell--stacked hidden"
          id="recently-viewed-products-cart"
          data-limit="3"
          data-target="api-upsell-product"
          data-animation="{{ animation_name }}"
          data-animation-duration="{{ animation_duration }}"
          data-animation-delay="{{ animation_cart_empty_delay }}"
        ></recently-viewed>
      {%- endif -%}

      <script>
        // Fix for cart items disappearing on browser back/forward navigation
        window.addEventListener('pageshow', function(event) {
          // Check if page was loaded from cache (back/forward navigation)
          if (event.persisted) {
            // Refresh cart page content when page is loaded from browser cache
            if (typeof window.theme !== 'undefined' && window.theme.routes) {
              // For cart page, we can simply reload the page to get fresh cart data
              window.location.reload();
            }
          }
        });
      </script>
    </div>

    <form id="cartForm" class="cart__form" action="{{ routes.cart_url }}" method="post" novalidate data-cart-form>
      {{ form.errors | default_errors }}

      <div class="cart__inner">
        <div class="cart__content">
          <h1
            class="cart__title"
            data-animation="{{ animation_name }}"
            data-animation-duration="{{ animation_duration }}"
            data-animation-delay="{{ animation_delay }}"
            {%- assign animation_delay = animation_delay | plus: animation_delay_increment -%}
          >
            {{ 'cart.general.title' | t }}

            <cart-count class="cart__items-count" data-cart-count="{{ cart_item_count }}">
              {{- cart_item_count -}}
            </cart-count>
          </h1>

          <div
            class="cart__button-continue"
            data-animation="{{ animation_name }}"
            data-animation-duration="{{ animation_duration }}"
            data-animation-delay="{{ animation_delay }}"
            {%- assign animation_delay = animation_delay | plus: animation_delay_increment -%}
          >
            <a class="btn btn--outline btn--primary" href="{{ routes.all_products_collection_url }}">
              <span>{{ 'cart.general.continue_shopping' | t }}</span>
            </a>
          </div>

          <div class="cart__errors" data-cart-errors>
            <div class="cart__errors__inner">
              <h5 class="cart__errors__heading">{{- 'cart.general.limit_message' | t -}}</h5>

              <p class="cart__errors__content" data-error-message></p>

              <button
                class="cart__errors__close"
                type="button"
                data-cart-error-close
                aria-label="{{ 'general.accessibility.close' | t }} {{ 'cart.general.title' | t }}"
              >
                {%- render 'icon-cancel' -%}
              </button>
            </div>
          </div>

          <cart-items
            class="cart__items"
            data-items-holder
            data-animation="{{ animation_name }}"
            data-animation-duration="{{ animation_duration }}"
            data-animation-delay="{{ animation_delay }}"
          >
            {%- render 'cart-line-items', part: 'line-items', animation_delay: animation_delay, is_cart: true -%}
            {%- assign animation_delay = animation_delay | plus: animation_delay_increment -%}
          </cart-items>
        </div>

        <div class="cart__aside" data-checkout-buttons>
          {%- assign animation_delay = animation_delay_initial -%}
          {%- if is_free_shipping_first -%}
            <div
              class="cart-block"
              data-animation="{{ animation_name }}"
              data-animation-duration="{{ animation_duration }}"
              data-animation-delay="{{ animation_delay }}"
              {{ free_shipping_block[0].shopify_attributes }}
            >
              {{- free_shipping -}}
              {%- assign animation_delay = animation_delay | plus: animation_delay_increment -%}
            </div>
          {%- endif -%}

          <div
            class="cart__price__holder"
            data-cart-price-holder
            data-animation="{{ animation_name }}"
            data-animation-duration="{{ animation_duration }}"
            data-animation-delay="{{ animation_delay }}"
          >
            {%- render 'cart-price' -%}
            {%- assign animation_delay = animation_delay | plus: animation_delay_increment -%}
          </div>

          <collapsible-elements single="true">
            {%- for block in section.blocks -%}
              {%- assign animation_delay = forloop.index
                | times: animation_delay_increment
                | plus: animation_delay_initial
              -%}

              <div
                class="cart-block"
                data-animation="{{ animation_name }}"
                data-animation-duration="{{ animation_duration }}"
                data-animation-delay="{{ animation_delay }}"
                {{ block.shopify_attributes }}
              >
                {%- case block.type -%}
                  {%- when '@app' -%}
                    {%- render block -%}

                  {%- when 'cart-message' -%}
                    {%- assign custom_message_color = block.settings.cart_custom_message_color -%}
                    {%- capture style -%}
                      {%- unless custom_message_color == 'rgba(0,0,0,0)' or custom_message_color == blank -%}
                        --text: {{ custom_message_color }};
                      {%- endunless -%}
                    {%- endcapture -%}

                    <div class="cart__message cart__message--custom" style="{{ style }}">
                      {{ block.settings.cart_custom_message_text }}
                    </div>

                  {%- when 'upsell-products' -%}
                    {%- render 'cart-block-upsell', block: block -%}

                  {%- when 'product-list' -%}
                    {%- render 'cart-block-upsell', block: block -%}

                  {%- when 'free-shipping' -%}
                    {%- unless is_free_shipping_first -%}
                      {{- free_shipping -}}
                    {%- endunless -%}

                  {%- when 'order-note' -%}
                    {%- render 'cart-block-order-note', block: block -%}

                  {%- when 'gift-note' -%}
                    {%- render 'cart-block-gift-note', block: block -%}

                  {%- when 'shipping-estimate' -%}
                    {%- render 'cart-block-shipping-calculator', block: block -%}

                  {%- when 'icon' -%}
                    {%- render 'icon', block: block -%}

                  {%- when 'payment-icons' -%}
                    {%- unless shop.enabled_payment_types == empty -%}
                      <ul class="cart__payment-icons">
                        {%- for type in shop.enabled_payment_types -%}
                          <li>{{ type | payment_type_svg_tag: class: 'payment-icon' }}</li>
                        {%- endfor -%}
                      </ul>
                    {%- endunless -%}

                  {%- when 'code' -%}
                    {%- if block.settings.code != blank -%}
                      {{ block.settings.code }}
                    {%- endif -%}

                  {%- when 'checkout-buttons' -%}
                    <div class="cart__foot__inner">
                      <button
                        type="submit"
                        name="update"
                        class="btn btn--full btn--outline btn--primary cart__checkout no-js"
                      >
                        {{- 'cart.general.update' | t -}}
                      </button>

                      <div class="cart__buttons-wrapper">
                        {%- if settings.enable_accept_terms -%}
                          <input type="hidden" name="attributes[{{ 'cart.general.accepted_terms' | t }}]" value="Yes">
                          <input
                            type="checkbox"
                            class="cart__acceptance__input"
                            name="acceptance"
                            id="acceptance"
                            data-cart-acceptance-checkbox
                          >
                          <label for="acceptance" class="cart__acceptance__label" data-cart-acceptance-label>
                            {{- settings.accept_terms_text -}}
                          </label>

                          <p class="cart__terms cart__errors__content" data-terms-error-message></p>
                        {%- endif -%}

                        <div
                          class="cart__buttons{% if settings.enable_accept_terms %} cart__buttons--disabled{% endif %}"
                          data-cart-checkout-buttons
                        >
                          <fieldset class="cart__buttons__fieldset" data-cart-checkout-button>
                            <button
                              type="submit"
                              name="checkout"
                              class="btn btn--full btn--primary btn--solid cart__checkout"
                            >
                              {%- if settings.show_lock_icon -%}
                                {%- render 'animated-icon', filename: 'icon-lock' -%}
                              {%- endif -%}

                              {{- 'cart.general.checkout' | t -}}
                            </button>

                            {%- if block.settings.enable_additional_buttons and additional_checkout_buttons -%}
                              <div class="additional-checkout-buttons additional-checkout-buttons--vertical">
                                {{- content_for_additional_checkout_buttons -}}
                              </div>
                            {%- endif -%}
                          </fieldset>
                        </div>
                      </div>

                      <em class="cart__note">
                        {{- 'cart.general.subtotal_note' | t -}}
                      </em>
                    </div>
                {%- endcase -%}
              </div>
            {%- endfor -%}
          </collapsible-elements>
        </div>
      </div>
    </form>
  </div>
</div>

{% schema %}
{
  "name": "Cart",
  "settings": [
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Top",
      "default": 80
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Bottom",
      "default": 50
    }
  ],
  "blocks": [
    {
      "type": "cart-message",
      "name": "Cart message",
      "limit": 1,
      "settings": [
        {
          "type": "richtext",
          "id": "cart_custom_message_text",
          "label": "Text",
          "default": "<p>Use coupon code <strong>WELCOME10</strong> for 10% off your first order.</p>"
        },
        {
          "type": "color",
          "id": "cart_custom_message_color",
          "label": "Text color"
        }
      ]
    },
    {
      "type": "free-shipping",
      "name": "Free shipping message",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Set a minimum amount for free shipping in [Theme settings](/admin/themes/current/editor?context=theme)"
        },
        {
          "type": "textarea",
          "id": "message",
          "label": "Message",
          "info": "Use ||amount|| to display progress towards free shipping.",
          "default": "You are ||amount|| away from free shipping."
        },
        {
          "type": "color_background",
          "id": "free_shipping_gradient",
          "label": "Custom progress bar gradient"
        }
      ]
    },
    {
      "type": "upsell-products",
      "name": "Upsell products",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "upsell_auto_open",
          "label": "Show upsell products automatically on cart page",
          "default": true
        }
      ]
    },
    {
      "type": "order-note",
      "name": "Order note",
      "limit": 1
    },
    {
      "type": "gift-note",
      "name": "Gift note",
      "limit": 1
    },
    {
      "type": "shipping-estimate",
      "name": "Shipping estimate",
      "limit": 1
    },
    {
      "type": "checkout-buttons",
      "name": "Checkout buttons",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "enable_additional_buttons",
          "label": "Enable additional checkout buttons",
          "default": false
        }
      ]
    },
    {
      "type": "icon",
      "name": "Icon",
      "settings": [
        {
          "type": "select",
          "id": "icon_name",
          "label": "Icon",
          "default": "icon-award",
          "options": [
            {"label": "Award", "value": "icon-award"},
            {"label": "Box", "value": "icon-box"},
            {"label": "Chat", "value": "icon-chat"},
            {"label": "Check", "value": "icon-check"},
            {"label": "Check circle", "value": "icon-check-circle"},
            {"label": "Cloud", "value": "icon-cloud"},
            {"label": "Diameter", "value": "icon-diameter"},
            {"label": "Discount", "value": "icon-discount"},
            {"label": "Donation", "value": "icon-donation"},
            {"label": "Droplet", "value": "icon-droplet"},
            {"label": "Info", "value": "icon-info-empty"},
            {"label": "Email", "value": "icon-email"},
            {"label": "Fast shipment", "value": "icon-fast-shipment"},
            {"label": "Flare", "value": "icon-flare"},
            {"label": "Flower", "value": "icon-flower"},
            {"label": "Gift", "value": "icon-gift"},
            {"label": "Green shipment", "value": "icon-green-shipment"},
            {"label": "Heart", "value": "icon-heart"},
            {"label": "Leaf", "value": "icon-leaf"},
            {"label": "Lightning", "value": "icon-lightning"},
            {"label": "Location", "value": "icon-location"},
            {"label": "Mail", "value": "icon-mail"},
            {"label": "Notes", "value": "icon-notes"},
            {"label": "Pants", "value": "icon-pants"},
            {"label": "Peace", "value": "icon-peace"},
            {"label": "Pin", "value": "icon-pin"},
            {"label": "Planet", "value": "icon-planet"},
            {"label": "Phone", "value": "icon-phone"},
            {"label": "Recycle", "value": "icon-recycle"},
            {"label": "Ruler", "value": "icon-ruler"},
            {"label": "Shield", "value": "icon-shield"},
            {"label": "Smile", "value": "icon-smile"},
            {"label": "Star", "value": "icon-star"},
            {"label": "Tree", "value": "icon-tree"},
            {"label": "Trophy", "value": "icon-trophy"},
            {"label": "Truck", "value": "icon-truck"},
            {"label": "Vegan", "value": "icon-vegan"},
            {"label": "Wash", "value": "icon-wash"},
            {"label": "Washing machine", "value": "icon-washing-machine"}
          ]
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Alternative icon/image",
          "info": "160 x 160px .svg recommended"
        },
        {
          "type": "range",
          "id": "icon_size",
          "label": "Size",
          "unit": "px",
          "min": 20,
          "max": 80,
          "step": 5,
          "default": 20
        },
        {
          "type": "color",
          "id": "icon_color",
          "label": "Icon color"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Title<\/p>"
        },
        {
          "type": "select",
          "id": "text_font_size",
          "label": "Text size",
          "info": "Automatically generated by the base size. Applies only when content in Text field is set to Paragraph.",
          "default": "body-medium",
          "options": [
            {"value": "body-x-small", "label": "Extra small"},
            {"value": "body-small", "label": "Small"},
            {"value": "body-medium", "label": "Medium"},
            {"value": "body-large", "label": "Large"},
            {"value": "body-x-large", "label": "Extra large"}
          ]
        }
      ]
    },
    {
      "type": "product-list",
      "name": "Product list",
      "settings": [
        {
          "type": "product_list",
          "id": "product_list",
          "label": "Product list",
          "limit": 3,
          "info": "Choose up to 3 products"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Buy it with"
        },
        {
          "type": "checkbox",
          "id": "upsell_auto_open",
          "label": "Show products list automatically on cart page",
          "default": false
        }
      ]
    },
    {
      "type": "code",
      "name": "Custom code",
      "settings": [
        {
          "type": "liquid",
          "id": "code",
          "label": "Custom code",
          "default": "<p style='margin:0;'>Once you write some custom code, it will render right here.</p>"
        }
      ]
    },
    {
      "type": "payment-icons",
      "name": "Payment icons",
      "settings": []
    },
    {
      "type": "@app"
    }
  ]
}
{% endschema %}
